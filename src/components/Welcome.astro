<div class="font-sans text-gray-800 dark:text-gray-200 max-w-4xl mx-auto p-8 bg-stone-50 dark:bg-gray-900 transition-colors">
	<div class="fixed top-0 left-0 w-full h-full -z-10 bg-gradient-to-br from-olive-green/5 via-transparent to-olive-green/5 dark:from-olive-green-dark-mode/10 dark:via-transparent dark:to-olive-green-dark-mode/10"></div>
	<main class="flex flex-col gap-8 justify-center">
		<header class="text-center mb-1 pb-8 border-b-2 border-olive-green dark:border-olive-green-dark-mode">
			<h1 class="text-olive-green dark:text-olive-green-dark-mode text-4xl m-0 tracking-widest uppercase"><PERSON></h1>
			<h2 class="text-2xl text-gray-500 dark:text-gray-400 mt-2 mb-6 font-normal uppercase">Senior Software Engineer</h2>
			<div class="flex sm:justify-center gap-3 sm:gap-6 flex-col sm:flex-row">
				<a href="https://www.linkedin.com/in/ivan-feofanov/" target="_blank" rel="noopener noreferrer" class="
					flex items-center gap-2 text-gray-800 dark:text-gray-200 no-underline py-2 px-4 rounded border border-olive-green-light dark:border-olive-green-dark-dark-mode transition-all duration-200 hover:bg-olive-green/10 dark:hover:bg-olive-green-dark-mode/20 hover:text-olive-green dark:hover:text-olive-green-dark-mode hover:fill-olive-green dark:hover:fill-olive-green-dark-mode
					">
					<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 256 256" class="fill-current"><path d="M216,24H40A16,16,0,0,0,24,40V216a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V40A16,16,0,0,0,216,24Zm0,192H40V40H216V216ZM96,112v64a8,8,0,0,1-16,0V112a8,8,0,0,1,16,0Zm88,28v36a8,8,0,0,1-16,0V140a20,20,0,0,0-40,0v36a8,8,0,0,1-16,0V112a8,8,0,0,1,15.79-1.78A36,36,0,0,1,184,140ZM100,84A12,12,0,1,1,88,72,12,12,0,0,1,100,84Z"></path></svg>
					LinkedIn
				</a>
				<a href="https://github.com/Ivan-Feofanov" target="_blank" rel="noopener noreferrer" class="
					flex items-center gap-2 text-gray-800 dark:text-gray-200 no-underline py-2 px-4 rounded border border-olive-green-light dark:border-olive-green-dark-dark-mode transition-all duration-200 hover:bg-olive-green/10 dark:hover:bg-olive-green-dark-mode/20 hover:text-olive-green dark:hover:text-olive-green-dark-mode hover:fill-olive-green dark:hover:fill-olive-green-dark-mode
					">
					<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 256 256" class="fill-current"><path d="M208.31,75.68A59.78,59.78,0,0,0,202.93,28,8,8,0,0,0,196,24a59.75,59.75,0,0,0-48,24H124A59.75,59.75,0,0,0,76,24a8,8,0,0,0-6.93,4,59.78,59.78,0,0,0-5.38,47.68A58.14,58.14,0,0,0,56,104v8a56.06,56.06,0,0,0,48.44,55.47A39.8,39.8,0,0,0,96,192v8H72a24,24,0,0,1-24-24A40,40,0,0,0,8,136a8,8,0,0,0,0,16,24,24,0,0,1,24,24,40,40,0,0,0,40,40H96v16a8,8,0,0,0,16,0V192a24,24,0,0,1,48,0v40a8,8,0,0,0,16,0V192a39.8,39.8,0,0,0-8.44-24.53A56.06,56.06,0,0,0,216,112v-8A58.14,58.14,0,0,0,208.31,75.68ZM200,112a40,40,0,0,1-40,40H112a40,40,0,0,1-40-40v-8a41.74,41.74,0,0,1,6.9-22.48A8,8,0,0,0,80,73.83a43.81,43.81,0,0,1,.79-33.58,43.88,43.88,0,0,1,32.32,20.06A8,8,0,0,0,119.82,64h32.35a8,8,0,0,0,6.74-3.69,43.87,43.87,0,0,1,32.32-20.06A43.81,43.81,0,0,1,192,73.83a8.09,8.09,0,0,0,1,7.65A41.72,41.72,0,0,1,200,104Z"></path></svg>
					GitHub
				</a>
				<a href="mailto:<EMAIL>" class="
					flex items-center gap-2 text-gray-800 dark:text-gray-200 no-underline py-2 px-4 rounded border border-olive-green-light dark:border-olive-green-dark-dark-mode transition-all duration-200 hover:bg-olive-green/10 dark:hover:bg-olive-green-dark-mode/20 hover:text-olive-green dark:hover:text-olive-green-dark-mode hover:fill-olive-green dark:hover:fill-olive-green-dark-mode
					">
					<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 256 256" class="fill-current"><path d="M224,48H32a8,8,0,0,0-8,8V192a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V56A8,8,0,0,0,224,48ZM203.43,64,128,133.15,52.57,64ZM216,192H40V74.19l82.59,75.71a8,8,0,0,0,10.82,0L216,74.19V192Z"></path></svg>
					<EMAIL>
				</a>
				<a href="/Ivan Feofanov CV.pdf" download class="
					flex items-center gap-2 text-gray-800 dark:text-gray-200 no-underline py-2 px-4 rounded border border-olive-green-light dark:border-olive-green-dark-dark-mode transition-all duration-200 hover:bg-olive-green/10 dark:hover:bg-olive-green-dark-mode/20 hover:text-olive-green dark:hover:text-olive-green-dark-mode hover:fill-olive-green dark:hover:fill-olive-green-dark-mode
					">
					<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 256 256" class="fill-current"><path d="M224,152a8,8,0,0,1-8,8H192v16h16a8,8,0,0,1,0,16H192v16a8,8,0,0,1-16,0V152a8,8,0,0,1,8-8h32A8,8,0,0,1,224,152ZM92,172a28,28,0,0,1-28,28H56v8a8,8,0,0,1-16,0V152a8,8,0,0,1,8-8H64A28,28,0,0,1,92,172Zm-16,0a12,12,0,0,0-12-12H56v24h8A12,12,0,0,0,76,172Zm88,8a36,36,0,0,1-36,36H112a8,8,0,0,1-8-8V152a8,8,0,0,1,8-8h16A36,36,0,0,1,164,180Zm-16,0a20,20,0,0,0-20-20h-8v40h8A20,20,0,0,0,148,180ZM40,112V40A16,16,0,0,1,56,24h96a8,8,0,0,1,5.66,2.34l56,56A8,8,0,0,1,216,88v24a8,8,0,0,1-16,0V96H152a8,8,0,0,1-8-8V40H56v72a8,8,0,0,1-16,0ZM160,80h28.69L160,51.31Z"></path></svg>
					Download CV
				</a>
			</div>
		</header>

		<section class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm dark:shadow-gray-900/20 mb-6 transition-colors">
			<h3 class="text-olive-green dark:text-olive-green-dark-mode text-xl mt-0 mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">PROFESSIONAL SUMMARY</h3>
			<p class="text-gray-800 dark:text-gray-200 leading-relaxed m-0 mb-4">
				Experienced Software Engineer with proven track record in designing and optimizing
				large-scale systems for scalability and efficiency, with a focus on problem-solving and innovative solutions.
				Known for successfully building complex systems, improving processes, and increasing operational
				efficiency through tailored backend solutions.
			</p>
		</section>

		<section class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm dark:shadow-gray-900/20 mb-6 transition-colors">
			<h3 class="text-olive-green dark:text-olive-green-dark-mode text-xl mt-0 mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">PERSONALITIES</h3>
			<div class="space-y-4">
				<div class="grid grid-cols-1 sm:grid-cols-3 gap-3 items-start">
					<span class="text-olive-green dark:text-olive-green-dark-mode font-semibold">Leadership & Responsibility:</span>
					<span class="sm:col-span-2 text-gray-800 dark:text-gray-200">Self-driven professional with strong leadership capabilities and a results-oriented mindset</span>
				</div>
				<div class="grid grid-cols-1 sm:grid-cols-3 gap-3 items-start">
					<span class="text-olive-green dark:text-olive-green-dark-mode font-semibold">Team-Focused:</span>
					<span class="sm:col-span-2 text-gray-800 dark:text-gray-200">Committed to fostering and protecting team dynamics while driving collective success</span>
				</div>
				<div class="grid grid-cols-1 sm:grid-cols-3 gap-3 items-start">
					<span class="text-olive-green dark:text-olive-green-dark-mode font-semibold">Strategic Approach:</span>
					<span class="sm:col-span-2 text-gray-800 dark:text-gray-200">Prefer methodical, step-by-step execution while maintaining flexibility to take calculated risks when necessary</span>
				</div>
				<div class="grid grid-cols-1 sm:grid-cols-3 gap-3 items-start">
					<span class="text-olive-green dark:text-olive-green-dark-mode font-semibold">Balanced Decision-Making:</span>
					<span class="sm:col-span-2 text-gray-800 dark:text-gray-200">Combine analytical thinking with adaptability to navigate complex technical challenges</span>
				</div>
			</div>
		</section>

		<section class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm dark:shadow-gray-900/20 mb-6 transition-colors">
			<h3 class="text-olive-green dark:text-olive-green-dark-mode text-xl mt-0 mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">SKILLS</h3>
			<div class="grid grid-cols-2">
				<div class="mb-5">
					<h4 class="my-2 text-olive-green-dark dark:text-olive-green-light-dark-mode">Programming languages:</h4>
					<ul class="list-none p-0 mt-2 mb-0">
						<li class="inline-block bg-olive-green/10 dark:bg-olive-green-dark-mode/20 text-olive-green-dark dark:text-olive-green-light-dark-mode py-1 px-3 rounded mr-2 mb-2">Golang</li>
						<li class="inline-block bg-olive-green/10 dark:bg-olive-green-dark-mode/20 text-olive-green-dark dark:text-olive-green-light-dark-mode py-1 px-3 rounded mr-2 mb-2">Python</li>
						<li class="inline-block bg-olive-green/10 dark:bg-olive-green-dark-mode/20 text-olive-green-dark dark:text-olive-green-light-dark-mode py-1 px-3 rounded mr-2 mb-2">JS/TS</li>
					</ul>
				</div>

				<div class="mb-5">
					<h4 class="my-2 text-olive-green-dark dark:text-olive-green-light-dark-mode">Communications:</h4>
					<ul class="list-none p-0 mt-2 mb-0">
						<li class="inline-block bg-olive-green/10 dark:bg-olive-green-dark-mode/20 text-olive-green-dark dark:text-olive-green-light-dark-mode py-1 px-3 rounded mr-2 mb-2">REST API</li>
						<li class="inline-block bg-olive-green/10 dark:bg-olive-green-dark-mode/20 text-olive-green-dark dark:text-olive-green-light-dark-mode py-1 px-3 rounded mr-2 mb-2">gRPC</li>
						<li class="inline-block bg-olive-green/10 dark:bg-olive-green-dark-mode/20 text-olive-green-dark dark:text-olive-green-light-dark-mode py-1 px-3 rounded mr-2 mb-2">NATS</li>
						<li class="inline-block bg-olive-green/10 dark:bg-olive-green-dark-mode/20 text-olive-green-dark dark:text-olive-green-light-dark-mode py-1 px-3 rounded mr-2 mb-2">RabbitMQ</li>
					</ul>
				</div>

				<div class="mb-5">
					<h4 class="my-2 text-olive-green-dark dark:text-olive-green-light-dark-mode">LLM:</h4>
					<ul class="list-none p-0 mt-2 mb-0">
						<li class="inline-block bg-olive-green/10 dark:bg-olive-green-dark-mode/20 text-olive-green-dark dark:text-olive-green-light-dark-mode py-1 px-3 rounded mr-2 mb-2">Gemini</li>
						<li class="inline-block bg-olive-green/10 dark:bg-olive-green-dark-mode/20 text-olive-green-dark dark:text-olive-green-light-dark-mode py-1 px-3 rounded mr-2 mb-2">OpenAI</li>
						<li class="inline-block bg-olive-green/10 dark:bg-olive-green-dark-mode/20 text-olive-green-dark dark:text-olive-green-light-dark-mode py-1 px-3 rounded mr-2 mb-2">Claude</li>
						<li class="inline-block bg-olive-green/10 dark:bg-olive-green-dark-mode/20 text-olive-green-dark dark:text-olive-green-light-dark-mode py-1 px-3 rounded mr-2 mb-2">PydanticAI</li>
						<li class="inline-block bg-olive-green/10 dark:bg-olive-green-dark-mode/20 text-olive-green-dark dark:text-olive-green-light-dark-mode py-1 px-3 rounded mr-2 mb-2">Langchain</li>
					</ul>
				</div>

				<div class="mb-5">
					<h4 class="my-2 text-olive-green-dark dark:text-olive-green-light-dark-mode">Databases:</h4>
					<ul class="list-none p-0 mt-2 mb-0">
						<li class="inline-block bg-olive-green/10 dark:bg-olive-green-dark-mode/20 text-olive-green-dark dark:text-olive-green-light-dark-mode py-1 px-3 rounded mr-2 mb-2">PostgreSQL</li>
						<li class="inline-block bg-olive-green/10 dark:bg-olive-green-dark-mode/20 text-olive-green-dark dark:text-olive-green-light-dark-mode py-1 px-3 rounded mr-2 mb-2">Redis</li>
						<li class="inline-block bg-olive-green/10 dark:bg-olive-green-dark-mode/20 text-olive-green-dark dark:text-olive-green-light-dark-mode py-1 px-3 rounded mr-2 mb-2">ScyllaDB</li>
					</ul>
				</div>

				<div class="mb-5 sm:mb-0">
					<h4 class="my-2 text-olive-green-dark dark:text-olive-green-light-dark-mode">CI/CD:</h4>
					<ul class="list-none p-0 mt-2 mb-0">
						<li class="inline-block bg-olive-green/10 dark:bg-olive-green-dark-mode/20 text-olive-green-dark dark:text-olive-green-light-dark-mode py-1 px-3 rounded mr-2 mb-2">Github Actions</li>
						<li class="inline-block bg-olive-green/10 dark:bg-olive-green-dark-mode/20 text-olive-green-dark dark:text-olive-green-light-dark-mode py-1 px-3 rounded mr-2 mb-2">GitlabCI</li>
					</ul>
				</div>

				<div class="mb-0">
					<h4 class="my-2 text-olive-green-dark dark:text-olive-green-light-dark-mode">Languages:</h4>
					<ul class="list-none p-0 mt-2 mb-0">
						<li class="inline-block bg-olive-green/10 dark:bg-olive-green-dark-mode/20 text-olive-green-dark dark:text-olive-green-light-dark-mode py-1 px-3 rounded mr-2 mb-2">English: C1</li>
						<li class="inline-block bg-olive-green/10 dark:bg-olive-green-dark-mode/20 text-olive-green-dark dark:text-olive-green-light-dark-mode py-1 px-3 rounded mr-2 mb-2">Russian: Native</li>
					</ul>
				</div>
			</div>
		</section>

		<section class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm dark:shadow-gray-900/20 mb-6 transition-colors">
			<h3 class="text-olive-green dark:text-olive-green-dark-mode text-xl mt-0 mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">WORK EXPERIENCE</h3>

			<div class="mb-8">
				<div class="mb-3">
					<div>
						<h4 class="my-2 text-olive-green-dark dark:text-olive-green-light-dark-mode">Senior Full Stack Engineer @ <a href="https://lido.fi/" target="_blank" rel="noopener noreferrer">Lido.fi</a></h4>
						<span class="text-gray-500 dark:text-gray-400 text-sm block mt-1">March 2024 - November 2024, Remote</span>
					</div>
				</div>
				<ul class="m-0 sm:ml-3 leading-relaxed text-gray-800 dark:text-gray-200">
					<li class="mb-2">Developed and improved an on-chain voting framework, reducing processing time by 20%, and improving efficiency for government operators.</li>
					<li class="mb-2">Established a test ownership distribution process, enhancing accountability and the sustainability of the protocol testing suite.</li>
					<li class="mb-0">Proposed and designed a new monitoring system that addressed pain points in the existing system, improving overall system reliability.</li>
				</ul>
			</div>

			<div class="mb-8">
				<div class="mb-3">
					<div>
						<h4 class="my-2 text-olive-green-dark dark:text-olive-green-light-dark-mode">Senior Backend Developer @ <a href="https://www.gostudent.org/" target="_blank" rel="noopener noreferrer">Gostudent</a></h4>
						<span class="text-gray-500 dark:text-gray-400 text-sm block mt-1">May 2022 - February 2024, Remote</span>
					</div>
				</div>
				<ul class="m-0 sm:ml-3 leading-relaxed text-gray-800 dark:text-gray-200">
					<li class="mb-2">Identified lead loss issues and developed a new processing system, reducing lead loss to zero and improving sales conversion rates.</li>
					<li class="mb-2">Built a static page generation system based on data gathered from multiple sources, reducing the time from weeks to 27 minutes for marketing page preparation.</li>
					<li class="mb-2">Took ownership of orphaned internal projects, aligning them with strict linting rules and increasing test coverage to 84%, up from 0% in some cases.</li>
					<li class="mb-0">Built a single source of truth for critical data types, centralizing management and reducing uncertainty.</li>
				</ul>
			</div>

			<div class="mb-8">
				<div class="mb-3">
					<div>
						<h4 class="my-2 text-olive-green-dark dark:text-olive-green-light-dark-mode">Senior Backend Developer @ <a href="https://bestdoctor.ru/" target="_blank" rel="noopener noreferrer">Bestdoctor</a></h4>
						<span class="text-gray-500 dark:text-gray-400 text-sm block mt-1">May 2021 – March 2022, Remote</span>
					</div>
				</div>
				<ul class="m-0 sm:ml-3 leading-relaxed text-gray-800 dark:text-gray-200">
					<li class="mb-2">Developed and deployed an ML post-processing system, replacing outdated spreadsheet-based workflows, and adding observability to enhance performance tracking.</li>
					<li class="mb-2">Designed and launched several key internal services, driving operational efficiency and fostering better collaboration within the development team.</li>
					<li class="mb-0">Created a robust business entity matching system, improving data handling capabilities and ensuring accurate matching despite data inaccuracies, resulting in a ~50% reduction in hand matching.</li>
				</ul>
			</div>

			<div class="mb-8">
				<div class="mb-3">
					<div>
						<h4 class="my-2 text-olive-green-dark dark:text-olive-green-light-dark-mode">Senior Backend Developer / TeamLead @ PIK Software</h4>
						<span class="text-gray-500 dark:text-gray-400 text-sm block mt-1">November 2017 – April 2021, Ekaterinburg</span>
					</div>
				</div>
				<ul class="m-0 sm:ml-3 leading-relaxed text-gray-800 dark:text-gray-200">
					<li class="mb-2">Architected and led development of the "Resident Personal Account" system, encompassing full-stack architecture design, service creation, and microservices maintenance using Python (Django + Django REST Framework) and Go.</li>
					<li class="mb-2">As Technical Lead, conducted comprehensive code reviews, provided architectural guidance, and oversaw deployment strategies across multiple company projects.</li>
					<li class="mb-0">Restructured development workflows and optimized CI/CD pipelines, resulting in enhanced system performance and operational stability.</li>
				</ul>
			</div>

			<div class="mb-8">
				<div class="mb-3">
					<div>
						<h4 class="my-2 text-olive-green-dark dark:text-olive-green-light-dark-mode">Backend Developer @ APIQA</h4>
						<span class="text-gray-500 dark:text-gray-400 text-sm block mt-1">December 2015 – November 2017, Ekaterinburg</span>
					</div>
				</div>
				<ul class="m-0 sm:ml-3 leading-relaxed text-gray-800 dark:text-gray-200">
					<li class="mb-2"><a href="https://dpathology.com" target="_blank" rel="noopener noreferrer">DPathology Platform</a> - Developed and maintained backend infrastructure for digital pathology services.</li>
					<li class="mb-2"><a href="https://dpathology.com" target="_blank" rel="noopener noreferrer">DPathology Platform</a> - Engineered a custom audio broadcasting library for DPathology educational initiatives.</li>
					<li class="mb-2"><a href="https://goldapple.ru" target="_blank" rel="noopener noreferrer">Golden Apple E-commerce</a> - Built scalable backend solutions for online retail platform. Successfully developed and launched an innovative marketing solution that raised sales up to 30%.</li>
					<li class="mb-0"><a href="https://grottbjorn.com" target="_blank" rel="noopener noreferrer">Grottbjorn Financial Services</a> - Developed comprehensive web platform and client portal for financial brokerage.</li>
				</ul>
			</div>
		</section>

		<section class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm dark:shadow-gray-900/20 mb-6 transition-colors">
			<h3 class="text-olive-green dark:text-olive-green-dark-mode text-xl mt-0 mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">CORE BELIEFS</h3>
			<p class="text-gray-800 dark:text-gray-200 leading-relaxed m-0 mb-4">
				No task is too complex; it's all about resource optimization and leveraging the right tools.
			</p>
			<p class="text-gray-800 dark:text-gray-200 leading-relaxed m-0 mb-4">
				The goal is not just to close tasks but to solve challenges with precision, ensuring long-term impact.
			</p>
		</section>

		<section class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm dark:shadow-gray-900/20 mb-6 transition-colors">
			<h3 class="text-olive-green dark:text-olive-green-dark-mode text-xl mt-0 mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">EXTRACURRICULAR ACTIVITIES</h3>
			<div class="mb-4">
				<h4 class="my-2 text-olive-green-dark dark:text-olive-green-light-dark-mode">Batumi Pet Shelter</h4>
				<ul class="m-0 sm:ml-3 leading-relaxed text-gray-800 dark:text-gray-200">
					<li class="mb-2">Developed shelter data management system to account for all pets, treatments, and medicines</li>
					<li class="mb-2">Developed shelter website <a href="https://petshelter.ge" target="_blank" rel="noopener noreferrer">https://petshelter.ge</a></li>
					<li class="mb-0">Assisted in organizing events, improving adoption rates, and managing daily shelter operations</li>
				</ul>
			</div>
		</section>

	</main>
</div>
