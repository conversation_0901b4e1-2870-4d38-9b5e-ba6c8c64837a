@import "tailwindcss";

@theme {
    /* Light theme colors */
    --color-olive-green: oklch(0.45 0.08 120);
    --color-olive-green-light: oklch(0.55 0.10 120);
    --color-olive-green-dark: oklch(0.35 0.06 120);

    /* Dark theme colors */
    --color-olive-green-dark-theme: oklch(0.65 0.12 120);
    --color-olive-green-light-dark-theme: oklch(0.75 0.14 120);
    --color-olive-green-dark-dark-theme: oklch(0.55 0.10 120);

    /* Theme-aware colors using CSS custom properties */
    --color-bg-primary: light-dark(oklch(0.98 0.01 120), oklch(0.15 0.02 120));
    --color-bg-secondary: light-dark(white, oklch(0.20 0.02 120));
    --color-bg-gradient-from: light-dark(oklch(0.98 0.01 120), oklch(0.12 0.02 120));
    --color-bg-gradient-to: light-dark(oklch(0.98 0.01 120), oklch(0.18 0.02 120));

    --color-text-primary: light-dark(oklch(0.25 0.02 120), oklch(0.90 0.02 120));
    --color-text-secondary: light-dark(oklch(0.45 0.02 120), oklch(0.70 0.02 120));
    --color-text-accent: light-dark(var(--color-olive-green), var(--color-olive-green-dark-theme));
    --color-text-accent-hover: light-dark(var(--color-olive-green-dark), var(--color-olive-green-light-dark-theme));

    --color-border-primary: light-dark(oklch(0.85 0.02 120), oklch(0.35 0.02 120));
    --color-border-accent: light-dark(var(--color-olive-green-light), var(--color-olive-green-dark-dark-theme));

    --color-shadow: light-dark(oklch(0.85 0.02 120 / 0.1), oklch(0.05 0.02 120 / 0.3));

    /* Skill tag colors */
    --color-skill-bg: light-dark(oklch(0.45 0.08 120 / 0.1), oklch(0.65 0.12 120 / 0.2));
    --color-skill-text: light-dark(var(--color-olive-green-dark), var(--color-olive-green-light-dark-theme));
}

@layer base {
    html {
        color-scheme: light dark;
    }

    body {
        background-color: var(--color-bg-primary);
        color: var(--color-text-primary);
        transition: background-color 0.3s ease, color 0.3s ease;
    }

    a {
        color: var(--color-text-accent);
        text-decoration: none;
        transition: color 0.2s ease, text-decoration 0.2s ease;
    }

    a:hover {
        color: var(--color-text-accent-hover);
        text-decoration: underline;
    }
}
